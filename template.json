{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "id": 1088, "iteration": 1755216363548, "links": [], "liveNow": false, "panels": [{"datasource": "VictoriaMetrics-1", "description": "Partition utilization, disk read, disk write, download bandwidth, upload bandwidth, if there are multiple network cards or multiple partitions, it is the value of the network card or partition with the highest utilization rate collected.\n\nCurrEstab: The number of TCP connections whose current status is ESTABLISHED or CLOSE-WAIT.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "filterable": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "nodename"}, "properties": [{"id": "displayName", "value": "Hostname"}, {"id": "unit", "value": "string"}, {"id": "decimals", "value": 1}, {"id": "custom.align", "value": "left"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "displayName", "value": "IP"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "links", "value": [{"targetBlank": false, "title": "Browse host details", "url": "d/xfpJB9FGz/node-exporter?orgId=1&var-job=${job}&var-hostname=All&var-node=${__cell}&var-device=All&var-origin_prometheus=$origin_prometheus"}]}, {"id": "custom.align", "value": null}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Memory"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align", "value": null}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "CPU Cores"}, {"id": "unit", "value": "short"}, {"id": "custom.align", "value": null}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": " Uptime"}, {"id": "unit", "value": "s"}, {"id": "decimals", "value": 2}, {"id": "custom.align", "value": "auto"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "displayName", "value": "CPU used"}, {"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "custom.align", "value": null}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 85}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #G"}, "properties": [{"id": "displayName", "value": "Memory Used"}, {"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "custom.align", "value": null}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 85}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #J"}, "properties": [{"id": "displayName", "value": "Download*"}, {"id": "unit", "value": "bps"}, {"id": "decimals", "value": 2}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "custom.align", "value": null}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 30485760}, {"color": "rgba(245, 54, 54, 0.9)", "value": 104857600}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #L"}, "properties": [{"id": "displayName", "value": "5m load"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align", "value": null}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hostname"}, "properties": [{"id": "custom.width", "value": 301}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Disk Usage (root)"}, "properties": [{"id": "color", "value": {"mode": "thresholds"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 90}]}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total CPU"}, "properties": [{"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Ram"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": " Uptime"}, "properties": [{"id": "custom.width", "value": 161}]}]}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 0}, "id": 4, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "Total CPU"}]}, "pluginVersion": "8.2.7", "targets": [{"exemplar": true, "expr": "node_uname_info{project=~\"$project\"} - 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "sum(time() - node_boot_time_seconds{project=~\"$project\"}) by (instance, project)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"exemplar": true, "expr": "node_memory_MemTotal_bytes{project=~\"$project\"} - 0", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"exemplar": true, "expr": "(1 - avg(irate(node_cpu_seconds_total{project=~\"$project\",mode=\"idle\"}[1m])) by (instance, project)) * 100", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "CPU使用率", "refId": "F"}, {"exemplar": true, "expr": "(1 - (node_memory_MemAvailable_bytes{project=~\"$project\"} / node_memory_MemTotal_bytes{project=~\"$project\"})) * 100", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "内存使用率", "refId": "G"}, {"exemplar": true, "expr": "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\",fstype!=\"rootfs\",project=~\"$project\"} * 100) \n       / node_filesystem_size_bytes{mountpoint=\"/\",fstype!=\"rootfs\",project=~\"$project\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P"}, {"exemplar": true, "expr": "count(node_cpu_seconds_total{mode=\"system\", project=~\"$project\"}) by (instance, project)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "Q"}, {"exemplar": true, "expr": "sum(node_memory_MemTotal_bytes{project=~\"$project\"}) by (instance, project)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "R"}, {"exemplar": true, "expr": "", "hide": false, "interval": "", "legendFormat": "", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Server Resource Overview【Project：$project】", "transformations": [{"id": "merge", "options": {"reducers": []}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "Value #B": true, "Value #C": true, "Value #D": false, "Value #F": false, "Value #G": false, "Value #L": true, "__name__": true, "class": true, "cluster": true, "device": true, "domainname": true, "fstype": true, "instance": false, "job": true, "machine": true, "mountpoint": true, "project": false, "release": true, "sysname": true, "version": true}, "indexByName": {"Time": 0, "Value #A": 10, "Value #B": 12, "Value #D": 11, "Value #F": 13, "Value #G": 15, "Value #P": 17, "Value #Q": 14, "Value #R": 16, "class": 21, "cluster": 22, "device": 18, "domainname": 1, "fstype": 19, "instance": 2, "job": 3, "machine": 5, "mountpoint": 20, "nodename": 6, "project": 4, "release": 7, "sysname": 8, "version": 9}, "renameByName": {"*************:9100": "Uptime", "Value #P": "Disk Usage (root)", "Value #Q": "Total CPU", "Value #R": "Total Ram", "instance": "", "project": "Project"}}}], "type": "table"}, {"datasource": "VictoriaMetrics-1", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 11}, "id": 2, "options": {"showHeader": true}, "pluginVersion": "8.2.7", "targets": [{"exemplar": true, "expr": "avg by (instance) (\n  avg_over_time(\n    (\n      1 - avg by (instance)(\n        rate(node_cpu_seconds_total{\n          project=~\"$project\",\n          instance=~\"$instance\",\n          mode=\"idle\"\n        }[$__rate_interval])\n      )\n    ) * 100\n  [$__range:])\n)", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Avg CPU usage (from ${__from:date:YYYY-MM-DD HH:mm:ss} to ${__to:date:YYYY-MM-DD HH:mm:ss})", "transformations": [{"id": "seriesToColumns", "options": {"byField": "Time"}}], "type": "table"}, {"datasource": "VictoriaMetrics-1", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 19}, "id": 5, "options": {"showHeader": true}, "pluginVersion": "8.2.7", "targets": [{"exemplar": true, "expr": "avg by (instance) (\n  avg_over_time(\n    (\n      1 - (\n        avg by (instance) (\n          node_memory_MemAvailable_bytes{\n            project=~\"$project\",\n            instance=~\"$instance\"\n          }\n        )\n        /\n        avg by (instance) (\n          node_memory_MemTotal_bytes{\n            project=~\"$project\",\n            instance=~\"$instance\"\n          }\n        )\n      )\n    ) * 100\n  [$__range:])\n)", "format": "table", "interval": "1d", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Avg Memory usage (from ${__from:date:YYYY-MM-DD HH:mm:ss} to ${__to:date:YYYY-MM-DD HH:mm:ss})", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Value #A": "Avg Memory usage"}}}], "type": "table"}, {"datasource": "VictoriaMetrics-1", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 27}, "id": 6, "options": {"showHeader": true}, "pluginVersion": "8.2.7", "targets": [{"exemplar": true, "expr": "avg by (instance) (\n  avg_over_time(\n    (\n      1 - (\n        avg by (instance) (\n          node_filesystem_avail_bytes{\n            project=~\"$project\",\n            instance=~\"$instance\",\n            fstype!~\"tmpfs|overlay|aufs|squashfs|iso9660\"\n          }\n        )\n        /\n        avg by (instance) (\n          node_filesystem_size_bytes{\n            project=~\"$project\",\n            instance=~\"$instance\",\n            fstype!~\"tmpfs|overlay|aufs|squashfs|iso9660\"\n          }\n        )\n      )\n    ) * 100\n  [$__range:])\n)", "format": "table", "interval": "1d", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Avg Disk usage (from ${__from:date:YYYY-MM-DD HH:mm:ss} to ${__to:date:YYYY-MM-DD HH:mm:ss})", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Value #A": "Avg Disk usage"}}}], "type": "table"}], "refresh": "", "schemaVersion": 32, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": ["admin"], "value": ["admin"]}, "datasource": "VictoriaMetrics-1", "definition": "label_values({job!=\"Ping_exporter\",job!=\"hbase\",job!~\"blackbox-.*\"},project)", "description": "Tenant name", "error": null, "hide": 0, "includeAll": true, "label": "Project", "multi": true, "name": "project", "options": [], "query": {"query": "label_values({job!=\"Ping_exporter\",job!=\"hbase\",job!~\"blackbox-.*\"},project)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "VictoriaMetrics-1", "definition": "label_values({project=~\"$project\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values({project=~\"$project\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Avg Usage by Project", "uid": "ZqNR5slHz", "version": 20}